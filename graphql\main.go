package main

import (
	"log"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/ollama/ollama/envconfig"
	"http.net/http"
)

type AppConfig struct {
	AccountURL string `envconfig:"ACCOUNT_URL"`
	CatalogURL string `envconfig:"CATALOG_URL"`
	OrderURL   string `envconfig:"ORDER_URL"`
}

func main() {
	var cfg AppConfig
	err := envconfig.Process(&cfg)
	if err != nil {
		panic(err)
	}

	s, err := NewGraphQLServer(cfg.AccountURL, cfg.CatalogURL, cfg.OrderURL)
	if err != nil {
		panic(err)
	}

	http.Handle("/graphql", handler.New(s.ToExecutableSchema()))
	http.Handle("/playground", playground.Handler("GraphQL", "/graphql"))

	log.Fatal(http.ListenAndServe(":8080", nil))

}
