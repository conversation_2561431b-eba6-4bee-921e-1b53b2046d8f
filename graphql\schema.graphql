

type Account{
    id: String!
    name: String!
    orders: [Order!]!
}

type Product{
    id: String!
    name: String!
    price: Float!
    description: String!
}

type Order{
    id: String!
    createAt: String!
    totalPrice: Float!
    items: [OrderItem!]!
}

type OrderItem{
    id: String!
    quantity: Int!
    product: Product!
    price: Float!
}

input PaginationInput{
    skip: Int!
    take: Int!
}


input AccountInput{
    name: String!
}

input ProductInput{
    name: String!
    price: Float!
    description: String!
}


input OrderItemInput{
    productId: String!
    quantity: Int!
}

input OrderInput{
    accountId: String!
    items: [OrderItemInput!]!
}

type Mutation{
    createAccount(input: AccountInput!): Account!
    createProduct(input: ProductInput!): Product!
    createOrder(input: OrderInput!): Order!
}

type Query{
    accounts(pagination: PaginationInput!, id: String): [Account!]!
    products(pagination: PaginationInput!, query: String, id: String): [Product!]!
}

